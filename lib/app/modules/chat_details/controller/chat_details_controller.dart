import 'dart:io';

import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:socket_io_client/socket_io_client.dart' as IO;
import 'package:get/get.dart';
import '../../../core/base/base_controller.dart';
import '../../../core/base/paging_controller.dart';
import '../../../core/model/chat_body_prem.dart';
import '../../../core/model/chat_query_prem.dart';
import '../../../data/model/chat_details_list_data_response.dart';
import '../../../data/model/user_data_response.dart';
import '../../../data/repository/chat_repository.dart';
import '../../../data/repository/pref_repository.dart';
import '../../../data/repository/user_repository.dart';
import '../model/chat_ui_data.dart';

class ChatDetailsController extends BaseController {
  var userId = '';
  final pagingController = PagingController<ChatUiData>();
  final ScrollController scrollController = ScrollController();

  final Rx<File?> file = Rx<File?>(null);
  var chatTextController = TextEditingController();
  final ChatRepository _repository = Get.find(tag: (ChatRepository).toString());
  final PrefRepository _pref = Get.find(tag: (PrefRepository).toString());
  final RxList<ChatUiData> _chatListController = RxList.empty();
  final UserRepository _userRepository = Get.find(
    tag: (UserRepository).toString(),
  );

  Future<void> pickFile(Rx<File?> file) async {
    FilePickerResult? result = await FilePicker.platform.pickFiles();

    if (result != null && result.files.single.path != null) {
      file.value = File(result.files.single.path!);
    } else {
      print('No file selected.');
    }
  }

  getUserData() async {
    var userService = _userRepository.getUserProfile('');
    callDataService(
      userService,
      onSuccess: _handleUserSuccess,
      onError: _handleUserError,
    );
  }

  _handleUserSuccess(UserDataResponse response) {
    connect("${response.user?.id}");

    userId = response.user!.id!;
  }

  _handleUserError(Exception exception) {}

  var recieverId = '';

  getString(String key) {
    return _pref.getString(key);
  }

  List<ChatUiData> get chatList => _chatListController.toList();
  late IO.Socket socket;

  getChatList() async {
    var queryParam = ChatQueryPrem(
      searchKeyWord: '',
      pageNumber: pagingController.pageNumber,
    );

    var response = _repository.getChatDetailList(queryParam, recieverId);
    callDataService(
      response,
      onSuccess: _handleGetChatSuccess,
      onError: _handleGetChatError,
    );
  }

  getLatestList() async {
    var queryParam = ChatQueryPrem(searchKeyWord: '', pageNumber: 1);

    var response = _repository.getChatDetailList(queryParam, recieverId);
    callDataService(
      response,
      onSuccess: _handleGetChatSuccess,
      onError: _handleGetChatError,
    );
  }

  sendChatMessage() async {
    var token = await getString("token");
    ChatBodyPrem chatBodyPrem = ChatBodyPrem(
      text: chatTextController.text,
      senderId: "",
      receiverId: "$recieverId",
      file: file.value,
    );
    var response = _repository.sendMessage(chatBodyPrem);
    callDataService(
      response,
      onSuccess: _handleSendChatSuccess,
      onError: _handleGetChatError,
    );
  }

  void clearFile() {
    file.value = null;
  }

  _handleSendChatSuccess(Map<String, dynamic> response) {
    chatTextController.clear();
    clearFile();
  }

  Future<void> connect(String id) async {
    var token = await getString("token");
    socket = IO.io(
      'http://192.168.0.243:6000',
      IO.OptionBuilder()
          .setTransports(['websocket'])
          .setReconnectionAttempts(5)
          .setAuth({"token": token})
          .setReconnectionDelay(2000)
          .build(),
    );

    // Connect to server
    socket.onConnect((_) async {});

    // Listen for private messages
    socket.on('receiveMessage', (data) {
      _chatListController.add(
        ChatUiData(
          message: data['text'] != null ? data['text'] : "",
          name: data['name'] != null ? data['text'] : "",
          time: data['createdAt'] != null
              ? data['createdAt']
              : "${DateTime.now().toString()}",
          avatarUrl: data['avatarUrl'] != null ? data['avatarUrl'] : "N/A",
          recieverId: data['recieverId'] != null ? data['recieverId'] : '',
          senderId: data['senderId'] != null ? data['senderId'] : '',
          file: data['file'] != null ? data['file'] : '',
        ),
      );
    });

    // Handle disconnect
    socket.onDisconnect((_) {
      print('Disconnected from server');
    });
  }

  @override
  void onInit() {
    getUserData();

    var arguments = Get.arguments;
    if (arguments is Map<String, dynamic>) {
      recieverId = arguments['userId'] ?? '';

      getChatList();
    }

    super.onInit();
  }

  bool _isLastPage(int newListItemCount, int totalCount) {
    return (chatList.length + newListItemCount) >= totalCount;
  }

  onLoadNextPage() {
    logger.i("On load next");

    getChatList();
  }

  _handleGetChatSuccess(ChatDetailsListDataResponse response) {
    List<ChatUiData>? repoList = response.chat?.map((e) {
      return ChatUiData(
        message: e.text != null ? e.text! : "",
        file: e.file != null ? e.file! : "Null",
        name: "",
        time: e.createdAt != null ? e.createdAt! : "Null",
        avatarUrl: "",
        senderId: e.senderId != null ? e.senderId! : '',
        recieverId: e.receiverId != null ? e.receiverId! : '',
      );
    }).toList();

    if (_isLastPage(repoList!.length, response.totalMessages!)) {
      pagingController.appendLastPage(repoList);
    } else {
      pagingController.appendPage(repoList);
    }

    var newList = [...pagingController.listItems];

    newList.sort(
      (a, b) => DateTime.parse(a.time).compareTo(DateTime.parse(b.time)),
    );

    _chatListController(newList);
  }

  _handleGetChatError(Exception exception) {}
}
