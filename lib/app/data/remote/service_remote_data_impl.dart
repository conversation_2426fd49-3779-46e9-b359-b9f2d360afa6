import 'package:dio/dio.dart';
import 'package:dio/src/response.dart';
import 'package:handyman_admin/app/core/model/promotion_body_prem.dart';
import 'package:handyman_admin/app/core/model/service_body_prem.dart';
import 'package:handyman_admin/app/core/model/service_cat_body_prem.dart';
import 'package:handyman_admin/app/core/model/service_cat_query_prem.dart';
import 'package:handyman_admin/app/data/remote/service_remote_data_source.dart';
import '../../core/base/base_remote_source.dart';
import '../../core/model/promotion_query_prem.dart';
import '../../core/model/service_search_query_prem.dart';
import '../../network/dio_provider.dart';
import '../model/get_service_cat_response.dart';
import '../model/service_promotion_response.dart';
import '../model/service_search_response.dart';

class ServiceRemoteDataImpl extends BaseRemoteSource
    implements ServiceRemoteDataSrc {
  @override
  Future<ServiceResponse> getAllService(
    ServiceSearchQueryParam searchQueryParam,
  ) {
    var endpoint = "${DioProvider.baseUrl}/services";
    var dioCall = dioClient.get(
      endpoint,
      queryParameters: searchQueryParam.toJson(),
    );

    try {
      return callApiWithErrorParser(
        dioCall,
      ).then((response) => _parseServiceSearchResponse(response));
    } catch (e) {
      rethrow;
    }
    throw UnimplementedError();
  }

  @override
  Future<Map<String, dynamic>> addService(
    ServiceBodyPrem serviceBodyPrem,
  ) async {
    final formData = FormData.fromMap({
      "serviceName": serviceBodyPrem.name,
      "cost": serviceBodyPrem.cost,
      "about": serviceBodyPrem.about,
      "serviceCategoryId": serviceBodyPrem.servieceCatId,
      'image': await MultipartFile.fromFile(
        serviceBodyPrem.image!.path,
        filename: serviceBodyPrem.image!.path,
      ),
    });
    var endpoint = "${DioProvider.baseUrl}/services";
    var dioCall = dioClient.post(endpoint, data: formData);

    try {
      return callApiWithErrorParser(dioCall).then((response) => response.data);
    } catch (e) {
      rethrow;
    }
    throw UnimplementedError();
  }

  @override
  Future<Map<String, dynamic>> addServiceCat(
    ServiceCatBodyPrem serviceCatBodyPrem,
  ) async {
    final formData = FormData.fromMap({
      "name": serviceCatBodyPrem.name,
      'image': await MultipartFile.fromFile(
        serviceCatBodyPrem.image!.path,
        filename: serviceCatBodyPrem.image!.path,
      ),
    });
    var endpoint = "${DioProvider.baseUrl}/service-categories";
    var dioCall = dioClient.post(endpoint, data: formData);

    try {
      return callApiWithErrorParser(dioCall).then((response) => response.data);
    } catch (e) {
      rethrow;
    }
    throw UnimplementedError();
  }

  @override
  Future<GetServiceCatResponse> getAllServiceCat(
    ServiceCatQueryPrem serviceCatQueryPrem,
  ) {
    var endpoint = "${DioProvider.baseUrl}/service-categories";
    var dioCall = dioClient.get(endpoint);
    try {
      return callApiWithErrorParser(
        dioCall,
      ).then((response) => _parseServiceCatSearchResponse(response));
    } catch (e) {
      rethrow;
    }
    throw UnimplementedError();
  }

  @override
  Future<ServicePromotionResponse> getAllBannerPromotion(
    PromotionQueryPrem promotionQueryPrem,
  ) {
    var endpoint = "${DioProvider.baseUrl}/promotions";
    var dioCall = dioClient.get(endpoint);
    try {
      return callApiWithErrorParser(
        dioCall,
      ).then((response) => _parsePromotionResponse(response));
    } catch (e) {
      rethrow;
    }

    throw UnimplementedError();
  }

  @override
  Future<Map<String, dynamic>> addPromotion(
    PromotionBodyPrem promotionBodyPrem,
  ) async {
    final formData = FormData.fromMap({
      "name": promotionBodyPrem.name,
      'photo': await MultipartFile.fromFile(
        promotionBodyPrem.image!.path,
        filename: promotionBodyPrem.image!.path,
      ),
    });
    var endpoint = "${DioProvider.baseUrl}/promotions";
    var dioCall = dioClient.post(endpoint, data: formData);

    try {
      return callApiWithErrorParser(dioCall).then((response) => response.data);
    } catch (e) {
      rethrow;
    }
    throw UnimplementedError();
  }

  @override
  Future<Map<String, dynamic>> deleteService(String serviceId) {
    var endpoint = "${DioProvider.baseUrl}/services/$serviceId";
    var dioCall = dioClient.delete(endpoint);
    try {
      return callApiWithErrorParser(dioCall).then((response) => response.data);
    } catch (e) {
      rethrow;
    }

    throw UnimplementedError();
  }

  @override
  Future<Map<String, dynamic>> deletePromotion(String promotionId) {
    var endpoint = "${DioProvider.baseUrl}/promotions/$promotionId";
    var dioCall = dioClient.delete(endpoint);
    try {
      return callApiWithErrorParser(dioCall).then((response) => response.data);
    } catch (e) {
      rethrow;
    }

    throw UnimplementedError();
  }

  @override
  Future<Map<String, dynamic>> deleteServiceCategory(String serviceCategoryId) {
    var endpoint =
        "${DioProvider.baseUrl}/service-categories/$serviceCategoryId";
    var dioCall = dioClient.delete(endpoint);
    try {
      return callApiWithErrorParser(dioCall).then((response) => response.data);
    } catch (e) {
      rethrow;
    }

    throw UnimplementedError();
  }

  @override
  Future<Map<String, dynamic>> updatePromotion(
    PromotionBodyPrem promotionBodyPrem,
    String id,
  ) async {
    final formData = promotionBodyPrem.image == null
        ? FormData.fromMap({"name": promotionBodyPrem.name})
        : FormData.fromMap({
            "name": promotionBodyPrem.name,
            'photo': await MultipartFile.fromFile(
              promotionBodyPrem.image!.path,
              filename: promotionBodyPrem.image!.path,
            ),
          });
    var endpoint = "${DioProvider.baseUrl}/promotions/$id";
    var dioCall = dioClient.put(endpoint, data: formData);

    try {
      return callApiWithErrorParser(dioCall).then((response) => response.data);
    } catch (e) {
      rethrow;
    }
    throw UnimplementedError();
  }

  @override
  Future<Map<String, dynamic>> updateServiceCategory(
    ServiceCatBodyPrem serviceCatBodyPrem,
    String id,
  ) async {
    final formData = serviceCatBodyPrem.image == null
        ? FormData.fromMap({"name": serviceCatBodyPrem.name})
        : FormData.fromMap({
            "name": serviceCatBodyPrem.name,
            'photo': await MultipartFile.fromFile(
              serviceCatBodyPrem.image!.path,
              filename: serviceCatBodyPrem.image!.path,
            ),
          });
    var endpoint = "${DioProvider.baseUrl}/service-categories/$id";
    var dioCall = dioClient.put(endpoint, data: formData);

    try {
      return callApiWithErrorParser(dioCall).then((response) => response.data);
    } catch (e) {
      rethrow;
    }
    throw UnimplementedError();
  }

  @override
  Future<Map<String, dynamic>> updateService(
    ServiceBodyPrem serviceBodyPrem,
    String serviceId,
  ) async {
    final formData = serviceBodyPrem.image == null
        ? FormData.fromMap({
            "serviceName": serviceBodyPrem.name,
            "cost": serviceBodyPrem.cost,
            "about": serviceBodyPrem.about,
          })
        : FormData.fromMap({
            "serviceName": serviceBodyPrem.name,
            "cost": serviceBodyPrem.cost,
            "about": serviceBodyPrem.about,
            'image': await MultipartFile.fromFile(
              serviceBodyPrem.image!.path,
              filename: serviceBodyPrem.image!.path,
            ),
          });
    var endpoint = "${DioProvider.baseUrl}/services/$serviceId";
    var dioCall = dioClient.put(endpoint, data: formData);

    try {
      return callApiWithErrorParser(dioCall).then((response) => response.data);
    } catch (e) {
      rethrow;
    }
    throw UnimplementedError();
  }
}

_parsePromotionResponse(Response response) {
  return ServicePromotionResponse.fromJson(response.data);
}

_parseServiceSearchResponse(Response response) {
  return ServiceResponse.fromJson(response.data);
}

_parseServiceCatSearchResponse(Response response) {
  return GetServiceCatResponse.fromJson(response.data);
}
