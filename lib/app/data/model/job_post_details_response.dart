class JobPostDetailsResponse {
  JobPost? jobPost;

  JobPostDetailsResponse({this.jobPost});

  JobPostDetailsResponse.fromJson(Map<String, dynamic> json) {
    jobPost = json['jobPost'] != null
        ? new JobPost.fromJson(json['jobPost'])
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.jobPost != null) {
      data['jobPost'] = this.jobPost!.toJson();
    }
    return data;
  }
}

class JobPost {
  String? id;
  String? createdAt;
  String? updatedAt;
  String? title;
  String? description;
  String? state;
  String? workingDay;
  String? addressId;
  bool? isComplete;
  String? originalPosterId;
  String? servieceId;
  String? taskPhoto;
  Address? address;
  OriginalPoster? originalPoster;
  Serviece? serviece;

  JobPost({
    this.id,
    this.createdAt,
    this.updatedAt,
    this.title,
    this.description,
    this.state,
    this.workingDay,
    this.addressId,
    this.isComplete,
    this.originalPosterId,
    this.servieceId,
    this.taskPhoto,
    this.address,
    this.originalPoster,
    this.serviece,
  });

  JobPost.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    createdAt = json['createdAt'];
    updatedAt = json['updatedAt'];
    title = json['title'];
    description = json['description'];
    state = json['status'];
    workingDay = json['scheduledTime'];
    addressId = json['addressId'];
    isComplete = json['isComplete'];
    originalPosterId = json['originalPosterId'];
    servieceId = json['serviceId'];
    taskPhoto = json['image'];
    address = json['address'] != null
        ? new Address.fromJson(json['address'])
        : null;
    originalPoster = json['originalPoster'] != null
        ? new OriginalPoster.fromJson(json['originalPoster'])
        : null;
    serviece = json['Serviece'] != null
        ? new Serviece.fromJson(json['Serviece'])
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['createdAt'] = this.createdAt;
    data['updatedAt'] = this.updatedAt;
    data['title'] = this.title;
    data['description'] = this.description;
    data['state'] = this.state;
    data['scheduledTime'] = this.workingDay;
    data['addressId'] = this.addressId;
    data['isComplete'] = this.isComplete;
    data['originalPosterId'] = this.originalPosterId;
    data['serviceId'] = this.servieceId;
    data['image'] = this.taskPhoto;
    if (this.address != null) {
      data['address'] = this.address!.toJson();
    }
    if (this.originalPoster != null) {
      data['originalPoster'] = this.originalPoster!.toJson();
    }
    if (this.serviece != null) {
      data['Serviece'] = this.serviece!.toJson();
    }
    return data;
  }
}

class Address {
  String? id;
  String? createdAt;
  String? updatedAt;
  String? city;
  String? street;
  String? zip;
  String? houseName;
  double? lat;
  double? lon;

  Address({
    this.id,
    this.createdAt,
    this.updatedAt,
    this.city,
    this.street,
    this.zip,
    this.houseName,
    this.lat,
    this.lon,
  });

  Address.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    createdAt = json['createdAt'];
    updatedAt = json['updatedAt'];
    city = json['city'];
    street = json['street'];
    zip = json['zip'];
    houseName = json['houseName'];
    lat = json['lat'];
    lon = json['lon'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['createdAt'] = this.createdAt;
    data['updatedAt'] = this.updatedAt;
    data['city'] = this.city;
    data['street'] = this.street;
    data['zip'] = this.zip;
    data['houseName'] = this.houseName;
    data['lat'] = this.lat;
    data['lon'] = this.lon;
    return data;
  }
}

class OriginalPoster {
  String? id;
  String? email;
  String? name;
  String? phone;
  String? profilePicture;
  String? password;
  String? token;

  OriginalPoster({
    this.id,
    this.email,
    this.name,
    this.phone,
    this.profilePicture,
    this.password,
    this.token,
  });

  OriginalPoster.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    email = json['email'];
    name = json['name'];
    phone = json['phone'];
    profilePicture = json['image'];
    password = json['password'];
    token = json['token'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['email'] = this.email;
    data['name'] = this.name;
    data['phone'] = this.phone;
    data['image'] = this.profilePicture;
    data['password'] = this.password;
    data['token'] = this.token;
    return data;
  }
}

class Serviece {
  String? id;
  String? name;
  String? image;
  String? cost;
  String? about;
  int? servieceCatId;

  Serviece({
    this.id,
    this.name,
    this.image,
    this.cost,
    this.about,
    this.servieceCatId,
  });

  Serviece.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    image = json['image'];
    cost = json['cost'];
    about = json['about'];
    servieceCatId = json['servieceCatId'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['name'] = this.name;
    data['image'] = this.image;
    data['cost'] = this.cost;
    data['about'] = this.about;
    data['servieceCatId'] = this.servieceCatId;
    return data;
  }
}
