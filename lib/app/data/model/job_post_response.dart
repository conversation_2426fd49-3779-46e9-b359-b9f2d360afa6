class JobPostResponse {
  List<Data>? data;
  int? currentPage;
  int? totalPages;
  int? totalJobPosts;

  JobPostResponse({
    this.data,
    this.currentPage,
    this.totalPages,
    this.totalJobPosts,
  });

  JobPostResponse.fromJson(Map<String, dynamic> json) {
    if (json['data'] != null) {
      data = <Data>[];
      json['data'].forEach((v) {
        data!.add(new Data.fromJson(v));
      });
    }
    currentPage = json['currentPage'];
    totalPages = json['totalPages'];
    totalJobPosts = json['totalJobPosts'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    data['currentPage'] = this.currentPage;
    data['totalPages'] = this.totalPages;
    data['totalJobPosts'] = this.totalJobPosts;
    return data;
  }
}

class Data {
  String? id;
  String? createdAt;
  String? updatedAt;
  String? title;
  String? description;
  String? status;
  String? workingDay;
  String? addressId;
  bool? isComplete;
  String? originalPosterId;
  String? servieceId;
  String? taskPhoto;

  Data({
    this.id,
    this.createdAt,
    this.updatedAt,
    this.title,
    this.description,
    this.status,
    this.workingDay,
    this.addressId,
    this.isComplete,
    this.originalPosterId,
    this.servieceId,
    this.taskPhoto,
  });

  Data.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    createdAt = json['createdAt'];
    updatedAt = json['updatedAt'];
    title = json['title'];
    description = json['description'];
    status = json['status'];
    workingDay = json['scheduledTime'];
    addressId = json['addressId'];
    isComplete = json['isComplete'];
    originalPosterId = json['originalPosterId'];
    servieceId = json['serviceId'];
    taskPhoto = json['image'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['createdAt'] = this.createdAt;
    data['updatedAt'] = this.updatedAt;
    data['title'] = this.title;
    data['description'] = this.description;
    data['status'] = this.status;
    data['scheduledTime'] = this.workingDay;
    data['addressId'] = this.addressId;
    data['isComplete'] = this.isComplete;
    data['originalPosterId'] = this.originalPosterId;
    data['serviceId'] = this.servieceId;
    data['image'] = this.taskPhoto;
    return data;
  }
}
