class ChatListDataResponse {
  List<Message>? message;

  ChatListDataResponse({this.message});

  ChatListDataResponse.fromJson(Map<String, dynamic> json) {
    if (json['message'] != null) {
      message = <Message>[];
      json['message'].forEach((v) {
        message!.add(new Message.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.message != null) {
      data['message'] = this.message!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class Message {
  String? userId;
  String? userName;
  String? latestMessage;
  String? timestamp;
  String? userPhoto;

  Message({
    this.userId,
    this.userName,
    this.latestMessage,
    this.timestamp,
    this.userPhoto,
  });

  Message.fromJson(Map<String, dynamic> json) {
    userId = json['userId'];
    userName = json['userName'];
    latestMessage = json['latestMessage'];
    timestamp = json['timestamp'];
    userPhoto = json['image'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['userId'] = this.userId;
    data['userName'] = this.userName;
    data['latestMessage'] = this.latestMessage;
    data['timestamp'] = this.timestamp;
    return data;
  }
}
